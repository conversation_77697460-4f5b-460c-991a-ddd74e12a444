'use server';

import { createErrorResponse } from '@/lib/server.action';
import { NextRequest } from 'next/server';

export async function GET(_: NextRequest, { params }: { params: Promise<{ segment: string[] }> }) {
    let response: Response;

    try {
        const { segment } = await params;
        const list = await fetch(`${process.env.INTERNAL_SERVER}/history/${segment[0]}/${segment[1]}`, {
            method: 'GET',
            headers: { 'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate', Pragma: 'no-cache', Expires: '0' },
            cache: 'no-store'
        });
        response = Response.json(await list.json(), { status: 200 });
    } catch (error) {
        response = createErrorResponse(error);
    }

    return response;
}
