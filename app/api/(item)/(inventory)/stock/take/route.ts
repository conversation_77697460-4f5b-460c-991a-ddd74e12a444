'use server';

import { STOCK_STATE } from '@/lib/const';
import redis from '@/lib/redis';
import { createErrorResponse } from '@/lib/server.action';
import { NextRequest } from 'next/server';

export async function POST(_: NextRequest) {
    let response: Response;

    try {
        await redis.set(STOCK_STATE.TAKE, 'true');
        fetch(`${process.env.INTERNAL_SERVER}/take/stock`, { method: 'GET' })
            .catch((error) => console.error(error))
            .then(async (response) => console.info(await response?.json()));
        response = Response.json({ archived: true }, { status: 200 });
    } catch (error) {
        response = createErrorResponse(error);
    }

    return response;
}
