'use client';

import { formatRp, getDefaultProduct, useSession } from '@/lib/client.action';
import { isRestricted } from '@/lib/server.action';
import { getList } from '@/queries/get';
import { DropdownItem } from '@/types/app';
import { isEmpty, last } from 'lodash';
import { Chart } from 'primereact/chart';
import { Column } from 'primereact/column';
import { DataTable } from 'primereact/datatable';
import { Dropdown } from 'primereact/dropdown';
import { Skeleton } from 'primereact/skeleton';
import { useEffect, useState } from 'react';

const mostBodyTemplate = (segment: string) => {
    const imageBody = ({ record }: any) => <img className="shadow-2" src={record?.images?.at(0) ?? getDefaultProduct()} alt={record?.name} width="50" />;
    const costBody = ({ count }: any) => formatRp(count);

    return { imageBody, costBody };
};

const StatHistory = () => {
    const [period, setPeriod] = useState<DropdownItem | undefined>();
    const [periods, setPeriods] = useState<DropdownItem[]>([]);
    const [analytic, setAnalytic] = useState<any>();
    const [loading, setLoading] = useState(true);
    const { scanning } = useSession();

    useEffect(() => {
        isRestricted().then(({ disabled }) => {
            if (disabled) {
                window.location.href = '/';
            }
        });

        scanning();
    }, [scanning]);

    useEffect(() => {
        const fetching = async () => {
            const options = await getList('history');
            const listing = options.map(({ period }) => ({ code: period, name: period }));

            if (!isEmpty(listing)) {
                const items = await getList(`history/analytics/${last(listing)?.code}`);
                setPeriod(last(listing));
                setAnalytic(last(items));
            }

            setPeriods(listing);
            setLoading(false);
        };

        setLoading(true);
        fetching();
    }, []);

    return (
        <div className="grid">
            <div className="col-12">
                <div className="card">
                    <div className="surface-0">
                        <ul className="list-none p-0 m-0 flex align-items-center font-medium mb-3">
                            <li>
                                <a href="/" className="text-500 no-underline line-height-3 cursor-pointer">
                                    App
                                </a>
                            </li>
                            <li className="px-2">
                                <i className="pi pi-angle-right text-500 line-height-3"></i>
                            </li>
                            <li>
                                <span className="text-900 line-height-3">Riwayat Statistik</span>
                            </li>
                        </ul>
                        <div className="flex align-items-start flex-column lg:justify-content-between lg:flex-row">
                            <div>
                                <div className="font-medium text-3xl text-900">Periode {period?.name ?? ' ..... '}</div>
                                <div className="flex align-items-center text-700 flex-wrap">
                                    <div className="mr-5 flex align-items-center mt-3">
                                        <i className="pi pi-calendar mr-2"></i>
                                        <span>Pilih Periode : </span>
                                    </div>
                                    <div className="mr-5 flex align-items-center mt-3">
                                        <Dropdown
                                            filter
                                            id="period"
                                            value={period}
                                            options={periods}
                                            optionLabel="name"
                                            placeholder="Periode Arsip"
                                            onChange={async ({ value }) => {
                                                setLoading(true);
                                                const items = await getList(`history/analytics/${value?.code}`);
                                                setPeriod(value);
                                                setAnalytic(items[0]);
                                                setLoading(false);
                                            }}
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {loading ? (
                <Skeleton width="100%" height="50em" />
            ) : (
                <>
                    <div className="col-12 lg:col-4">
                        <div className="card mb-0">
                            <div className="flex justify-content-between mb-3">
                                <div>
                                    <span className="block text-500 font-medium mb-3">Pengadaan</span>
                                    <div className="text-900 font-medium text-xl">{analytic?.receipts ?? 0}</div>
                                </div>
                                <div className="flex align-items-center justify-content-center bg-blue-100 border-round" style={{ width: '2.5rem', height: '2.5rem' }}>
                                    <i className="pi pi-box text-blue-500 text-xl" />
                                </div>
                            </div>
                            <span className="text-blue-500 font-medium">{formatRp(analytic?.amount?.receipts ?? 0)}</span>
                        </div>
                    </div>
                    <div className="col-12 lg:col-4">
                        <div className="card mb-0">
                            <div className="flex justify-content-between mb-3">
                                <div>
                                    <span className="block text-500 font-medium mb-3">Pendapatan</span>
                                    <div className="text-900 font-medium text-xl">{analytic?.sales ?? 0}</div>
                                </div>
                                <div className="flex align-items-center justify-content-center bg-green-100 border-round" style={{ width: '2.5rem', height: '2.5rem' }}>
                                    <i className="pi pi-money-bill text-green-500 text-xl" />
                                </div>
                            </div>
                            <span className="text-green-500 font-medium">{formatRp(analytic?.amount?.sales?.revenue ?? 0)}</span>
                        </div>
                    </div>
                    <div className="col-12 lg:col-4">
                        <div className="card mb-0">
                            <div className="flex justify-content-between mb-3">
                                <div>
                                    <span className="block text-500 font-medium mb-3">Pajak</span>
                                    <div className="text-900 font-medium text-xl">{analytic?.amount?.sales?.tax?.count ?? 0}</div>
                                </div>
                                <div className="flex align-items-center justify-content-center bg-purple-100 border-round" style={{ width: '2.5rem', height: '2.5rem' }}>
                                    <i className="pi pi-calculator text-purple-500 text-xl" />
                                </div>
                            </div>
                            <span className="text-purple-500 font-medium">{formatRp(analytic?.amount?.sales?.tax?.amount ?? 0)}</span>
                        </div>
                    </div>
                    <div className="col-12 lg:col-4">
                        <div className="card mb-0">
                            <div className="flex justify-content-between mb-3">
                                <div>
                                    <span className="block text-500 font-medium mb-3">Hutang</span>
                                    <div className="text-900 font-medium text-xl">{analytic?.amount?.debts?.count?.debt ?? 0}</div>
                                </div>
                                <div className="flex align-items-center justify-content-center bg-red-100 border-round" style={{ width: '2.5rem', height: '2.5rem' }}>
                                    <i className="pi pi-credit-card text-red-500 text-xl" />
                                </div>
                            </div>
                            <span className="text-red-500 font-medium">{formatRp(analytic?.amount?.debts?.amount?.debt ?? 0)}</span>
                        </div>
                    </div>
                    <div className="col-12 lg:col-4">
                        <div className="card mb-0">
                            <div className="flex justify-content-between mb-3">
                                <div>
                                    <span className="block text-500 font-medium mb-3">Piutang</span>
                                    <div className="text-900 font-medium text-xl">{analytic?.amount?.debts?.count?.loan ?? 0}</div>
                                </div>
                                <div className="flex align-items-center justify-content-center bg-orange-100 border-round" style={{ width: '2.5rem', height: '2.5rem' }}>
                                    <i className="pi pi-wallet text-orange-500 text-xl" />
                                </div>
                            </div>
                            <span className="text-orange-500 font-medium">{formatRp(analytic?.amount?.debts?.amount?.loan ?? 0)}</span>
                        </div>
                    </div>
                    <div className="col-12">
                        <hr />
                    </div>

                    <div className="col-12">
                        <div className="card">
                            <h5>Grafik Pendapatan Bulanan</h5>
                            <Chart
                                type="line"
                                data={{ labels: analytic?.analytics?.result?.labels ?? [], datasets: analytic?.analytics?.result?.datasets ?? [] }}
                                style={{ marginTop: '50px', marginBottom: '50px' }}
                                options={{
                                    plugins: { legend: { labels: { color: '#495057' } } },
                                    scales: {
                                        x: { ticks: { color: '#495057' }, grid: { color: '#ebedef' } },
                                        y: { ticks: { color: '#495057' }, grid: { color: '#ebedef' } }
                                    }
                                }}
                            />
                        </div>
                    </div>
                    <div className="col-12 lg:col-6">
                        <div className="card">
                            <h5>Tabel Pendapatan Bulanan</h5>
                            <DataTable value={analytic?.analytics?.result?.tables ?? []} rows={7} paginator scrollable>
                                <Column header="Periode" field="period" sortable />
                                <Column header="Kotor" field="income" sortable />
                                <Column header="Bersih" field="nett" sortable />
                                <Column header="Piutang" field="loan" sortable />
                                <Column header="Kas" field="revenue" sortable />
                            </DataTable>
                        </div>
                    </div>

                    <div className="col-12 lg:col-6">
                        <div className="card">
                            <h5>Barang Terlaris</h5>
                            <DataTable value={analytic?.analytics?.top?.product ?? []} rows={5} paginator scrollable>
                                <Column header="Gambar" filterField="record.name" body={mostBodyTemplate('product').imageBody} style={{ width: '10%' }} />
                                <Column header="Produk" field="record.name" sortable />
                                <Column header="Transaksi" field="count" sortable />
                            </DataTable>
                        </div>
                    </div>
                    <div className="col-12 lg:col-6">
                        <div className="card">
                            <h5>Pelanggan Teratas</h5>
                            <DataTable value={analytic?.analytics?.top?.customer ?? []} rows={5} paginator scrollable>
                                <Column header="Pelanggan" field="record.name" sortable />
                                <Column header="Total" filterField="count" sortable body={mostBodyTemplate('customer').costBody} />
                            </DataTable>
                        </div>
                    </div>
                    <div className="col-12 lg:col-6">
                        <div className="card">
                            <h5>Kategori Terlaris</h5>
                            <DataTable value={analytic?.analytics?.top?.category ?? []} rows={5} paginator scrollable>
                                <Column header="Kategori" field="record.name" sortable />
                                <Column header="Transaksi" field="count" sortable />
                            </DataTable>
                        </div>
                    </div>
                </>
            )}
        </div>
    );
};

export default StatHistory;
