import { DebitStatus } from './enum';

const STOCK_STATE = { SYNC: 'sync:stock', SYNCING: 'syncing:stock', CACHED: 'cached:stock', TAKE: 'take:stock', STOP: 'stop:stock' };

const INVENTORY_CACHED = { SALES: 'cached:sales', RECEIPT: 'cached:receipt' };

const SESSION_STATE = { KEY: 'session:state' };

const DEBT_STATE: { [key: string]: string } = { [DebitStatus.instalment]: 'Belum Lunas', [DebitStatus.paid]: 'Lunas', [DebitStatus.unpaid]: 'Belum Dibayar' };

const DEBT_SEVERITY: { [key: string]: 'green' | 'orange' | 'red' } = { [DebitStatus.instalment]: 'orange', [DebitStatus.paid]: 'green', [DebitStatus.unpaid]: 'red' };

const APP_METADATA = {
    title: process.env.NEXT_PUBLIC_APP_NAME,
    description: process.env.NEXT_PUBLIC_DESCRIPTION,
    robots: { index: false, follow: false },
    viewport: { initialScale: 1, width: 'device-width' },
    icons: { icon: '/favicon.ico' }
};

export interface ComponentToPrintProps {
    id?: string;
}

export { APP_METADATA, DEBT_SEVERITY, DEBT_STATE, INVENTORY_CACHED, SESSION_STATE, STOCK_STATE };
