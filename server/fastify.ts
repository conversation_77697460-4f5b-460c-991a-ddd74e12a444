import { calculateDataSheetMonthly, calculateSumCost } from '@/lib/client.action';
import { STOCK_STATE } from '@/lib/const';
import { syncStock } from '@/mutations/item/inventory/stock/sync';
import { cleansing } from '@/mutations/item/inventory/stock/taking';
import { prepareStockTaking, processAnalytics } from '@/queries/get';
import cors from '@fastify/cors';
import fastifyRedis, { FastifyRedis } from '@fastify/redis';
import dayjs from 'dayjs';
import 'dayjs/locale/id';
import fastify from 'fastify';
import fastifyCron from 'fastify-cron';
import { promises } from 'fs';
import { first, isEmpty, last, round, sample } from 'lodash';
import nano, { DocumentScope, MangoResponse } from 'nano';
import puppeteer from 'puppeteer';
import { getPrinters, isPrintComplete, print } from 'unix-print';
import { ExecResponse, Printer } from 'unix-print/build/types';

dayjs.locale('id');

// Initialize database connection
const handshakeDB = async () => {
    let couchdb: DocumentScope<any> | undefined;
    const client = nano(process.env.COUCHDB_URL ?? '');

    try {
        if (process.env.MONGO_DATABASE) {
            // Create DB if not exists
            const exists = await client.db.list();

            if (!exists.includes(process.env.MONGO_DATABASE)) {
                await client.db.create(process.env.MONGO_DATABASE);
            }

            couchdb = client.db.use(process.env.MONGO_DATABASE);

            // Create index for efficient queries
            if (couchdb) {
                await couchdb.createIndex({ index: { fields: ['model', 'period'] }, name: 'archive_index' });
            }
        }
    } catch (error) {
        console.error('Cannot connect to CouchDB !!!', error);
    }

    return couchdb ?? null;
};

const cleaner = () => {
    const dir = 'public/storage/document';
    console.info('Janitor running ...');

    // Handle async operations without blocking the cron job
    promises
        .stat(dir)
        .then((stat) => {
            if (stat.isDirectory()) {
                return promises.readdir(dir);
            }

            return [];
        })
        .then((junks) => {
            for (const junk of junks) {
                promises
                    .stat(`${dir}/${junk}`)
                    .then((trash) => {
                        if (trash.isFile()) {
                            const names = junk.split('-');

                            if (['receipt', 'sales'].includes(first(names) ?? '') && last(names)?.toLowerCase()?.endsWith('.pdf')) {
                                const timestamp = Number(last(names)?.replace('.pdf', ''));

                                if (dayjs().diff(dayjs.unix(timestamp), 'minutes') >= 3) {
                                    promises.unlink(`${dir}/${junk}`).catch((error) => {
                                        console.error('Error deleting file:', junk, error);
                                    });
                                }
                            }
                        }
                    })
                    .catch((error) => {
                        console.error('Error checking file:', junk, error);
                    });
            }
        })
        .catch((error) => {
            console.error('Error in cron job:', error);
        });
};

const analyzing = (redis: FastifyRedis) => {
    redis.get(STOCK_STATE.SYNC).then(async (state) => {
        if (!state) {
            const archiving = await redis.get(STOCK_STATE.TAKE);

            if (!archiving) {
                console.info('Analytics running ...');
                await processAnalytics();
            }
        }
    });
};

const archiveSort = async (couchdb: DocumentScope<any>, model: string) => {
    const sorter: { [key: string]: any[] } = {
        products: [{ inventory: 'desc' }],
        receipts: [{ date: 'desc' }, { 'author.created.time': 'desc' }, { reference: 'desc' }],
        sales: [{ date: 'desc' }, { 'author.created.time': 'desc' }, { reference: 'desc' }],
        debts: [{ date: 'desc' }, { 'author.created.time': 'desc' }, { money: 'desc' }]
    };

    if (model === 'products') {
        await couchdb.createIndex({ index: { fields: ['inventory'] }, name: 'inventory_index' });
    } else if (['receipts', 'sales'].includes(model)) {
        await couchdb.createIndex({ index: { fields: ['date', 'author.created.time', 'reference'] }, name: 'transaction_index' });
    } else {
        await couchdb.createIndex({ index: { fields: ['date', 'author.created.time', 'money'] }, name: 'debt_index' });
    }

    return sorter[model];
};

const pickRandPrinter = async () => {
    let printer: Printer | undefined;

    try {
        const printers = await getPrinters();
        printer = sample(printers.filter(({ status }) => status === 'idle'));
    } catch (error) {
        console.error(error);
    }

    return printer ?? null;
};

const awaitPrinting = async (job: ExecResponse) => {
    while (!(await isPrintComplete(job))) {
        await new Promise((resolve) => setTimeout(resolve, 1500)); // Wait for 1.5 seconds
    }

    return true;
};

const _takingProducts = async (couchdb: DocumentScope<any>, products: any[], model: string, period: string) => {
    let counter = 0;
    let saved = 0;

    console.info('Archiving products ...');
    for (const product of products) {
        let inventory = product?.inventory ?? 0;
        const category = product?.category ?? null;
        const unit = product?.unit ?? null;
        const bundle = product?.bundle ?? null;
        const author = product.author;
        delete category?._id;
        delete unit?._id;
        delete bundle?.node?.unit?._id;
        delete bundle?.contain?.unit?._id;
        delete author.created?.by?._id;
        delete author.edited?.by?._id;
        delete author.deleted?.by?._id;

        if (inventory < 0) {
            inventory = 0;
        }

        const inserted = await couchdb.insert({ logged: new Date(), id: String(product?._id), name: product?.name, initialCost: product?.initialCost, cost: product?.cost ?? [0, 0], inventory, bundle, category, unit, author, period, model });
        counter++;

        if (inserted.ok) {
            console.info('Done archiving product :', counter, ' of ', products.length);
            saved++;
        }
    }
    console.info('Archiving products done');

    return products.length === saved;
};

const _takingReceipts = async (couchdb: DocumentScope<any>, receipts: any[], model: string, period: string) => {
    let counter = 0;
    let saved = 0;

    console.info('Archiving receipts ...');
    for (const receipt of receipts) {
        const supplier = receipt?.supplier ?? null;
        const author = receipt.author;
        delete supplier?._id;
        delete author.created?.by?._id;
        delete author.edited?.by?._id;
        delete author.deleted?.by?._id;

        const products = (receipt.products as any[]).map(({ product, unit, qty, cost, discount }) => {
            delete product?._id;
            delete product?.category?._id;
            delete product?.unit?._id;
            delete unit?._id;

            return { product, unit, qty, cost, discount };
        });

        const inserted = await couchdb.insert({ logged: new Date(), id: String(receipt?._id), reference: receipt?.reference, date: receipt?.date ?? null, supplier, products, author, period, model });
        counter++;

        if (inserted.ok) {
            console.info('Done archiving receipt :', counter, ' of ', receipts.length);
            saved++;
        }
    }
    console.info('Archiving receipts done');

    return receipts.length === saved;
};

const _takingSales = async (couchdb: DocumentScope<any>, sales: any[], model: string, period: string) => {
    let counter = 0;
    let saved = 0;

    console.info('Archiving sales ...');
    for (const sale of sales) {
        const customer = sale?.customer ?? null;
        const author = sale.author;
        delete customer?._id;
        delete author.created?.by?._id;
        delete author.edited?.by?._id;
        delete author.deleted?.by?._id;

        const products = (sale.products as any[]).map(({ product, salesQty, bonusQty, price, discount }) => {
            delete product?._id;
            delete product?.category?._id;
            delete product?.unit?._id;
            delete salesQty?.unit?._id;
            delete bonusQty?.unit?._id;

            return { product, salesQty, bonusQty, price, discount };
        });

        const inserted = await couchdb.insert({
            logged: new Date(),
            id: String(sale?._id),
            reference: sale?.reference,
            subPrice: sale?.subPrice,
            finalPrice: sale?.finalPrice,
            paid: sale?.paid,
            change: sale?.change ?? 0,
            tax: sale?.tax ?? 0,
            date: sale?.date ?? null,
            customer,
            products,
            author,
            period,
            model
        });
        counter++;

        if (inserted.ok) {
            console.info('Done archiving sales :', counter, ' of ', sales.length);
            saved++;
        }
    }
    console.info('Archiving sales done');

    return sales.length === saved;
};

const _takingPaid = async (couchdb: DocumentScope<any>, hasPaid: any[], model: string, period: string) => {
    let counter = 0;
    let saved = 0;

    console.info('Archiving has paid ...');
    for (const paid of hasPaid) {
        const debt = paid?.debt ?? null;
        const loan = paid?.loan ?? null;
        const author = paid.author;
        delete debt?.supplier?._id;
        delete loan?.customer?._id;
        delete author.created?.by?._id;
        delete author.edited?.by?._id;
        delete author.deleted?.by?._id;

        const inserted = await couchdb.insert({
            logged: new Date(),
            id: String(paid?._id),
            money: paid?.money,
            status: paid?.status,
            instalment: paid?.instalment,
            date: paid?.date ?? null,
            debt,
            loan,
            author,
            period,
            model
        });
        counter++;

        if (inserted.ok) {
            console.info('Done archiving has paid :', counter, ' of ', hasPaid.length);
            saved++;
        }
    }
    console.info('Archiving has paid done');

    return hasPaid.length === saved;
};

const _countBuys = (list: any[]) => {
    let cost = 0;

    list.forEach(({ products }: any) => {
        cost += calculateSumCost(products);
    });

    return round(cost);
};

const _countSells = (list: any[]) => {
    let revenue = 0;
    let tax = 0;

    list.forEach(({ subPrice, finalPrice }: any) => {
        revenue += subPrice;
        tax += finalPrice - subPrice;
    });

    return { revenue, tax: { count: list.filter(({ tax }) => tax > 0).length, amount: tax } };
};

const _countDebts = (list: any[]) => {
    let sumDebt = 0;
    let sumLoan = 0;

    list.forEach(({ money, debt, loan }) => {
        if (debt) {
            sumDebt += money;
        }

        if (loan) {
            sumLoan += money;
        }
    });

    return { debt: sumDebt, loan: sumLoan };
};

const stockTaking = async (redis: FastifyRedis) => {
    const archiveDb = await handshakeDB();
    const period = dayjs().format('MMM-YYYY');
    let done = false;

    if (archiveDb) {
        const { products, receipts, sales, hasPaid } = await prepareStockTaking();
        const productArchived = await _takingProducts(archiveDb, products, 'products', period);
        const receiptArchived = await _takingReceipts(archiveDb, receipts, 'receipts', period);
        const salesArchived = await _takingSales(archiveDb, sales, 'sales', period);
        const paidArchived = await _takingPaid(archiveDb, hasPaid, 'debts', period);
        done = productArchived && receiptArchived && salesArchived && paidArchived;

        if (done) {
            console.info('Resetting inventory ...');
            const taken = await archiveDb.find({ selector: { period, model: 'period' }, limit: 1 });

            if (isEmpty(taken.docs)) {
                await archiveDb.insert({ period, model: 'period', logged: new Date() });
            }

            console.info('Processing analytics result ...');
            const analytics = await processAnalytics();
            console.info('Saving analytics result ...');
            await archiveDb.insert({
                period,
                model: 'sum',
                logged: new Date(),
                products: products.length,
                receipts: receipts.length,
                sales: sales.length,
                debts: hasPaid.length,
                amount: {
                    receipts: _countBuys(receipts),
                    sales: _countSells(sales),
                    debts: { amount: _countDebts(hasPaid), count: { debt: hasPaid.filter(({ debt }) => debt !== null).length, loan: hasPaid.filter(({ loan }) => loan !== null).length } }
                },
                analytics: {
                    result: calculateDataSheetMonthly(analytics?.records?.sales?.all ?? [], analytics?.records?.loans ?? []),
                    top: { product: analytics?.records?.highest?.products ?? [], customer: analytics?.records?.highest?.customers ?? [], category: analytics?.records?.highest?.categories ?? [] }
                }
            });
            await cleansing(products.map((item) => ({ product: item?._id, cost: (item?.cost as any[])?.at(1) ?? (item?.cost as any[])?.at(0) ?? item?.initialCost ?? 0, inventory: item?.inventory ?? 0 })));
            await redis.del(STOCK_STATE.CACHED, 'analytics');
        }

        console.info('Stock taking done', done);
    }

    return done;
};

const getArchivedRecords = async (model: string, period?: string) => {
    let records: any[] = [];

    try {
        const archiveDb = await handshakeDB();

        if (archiveDb) {
            let list: MangoResponse<any> | undefined;

            if (period) {
                const limiter = await archiveDb.find({ selector: { model: 'sum', period }, limit: 1 });

                if (model === 'analytics') {
                    list = limiter;
                } else {
                    list = await archiveDb.find({ selector: { model, period }, sort: await archiveSort(archiveDb, model), limit: (limiter?.docs[0]?.[model] ?? 1) * 2 });
                }
            } else {
                list = await archiveDb.find({ selector: { model } });
            }

            records = list?.docs ?? [];
        }
    } catch (error) {
        console.error(error);
    }

    return records;
};

const goRun = async () => {
    const server = fastify();

    try {
        server.register(cors, { origin: '*' });
        server.register(fastifyRedis, { url: process.env.REDIS_URL });
        server.register(fastifyCron, {
            jobs: [
                { cronTime: '0 */2 * * *', start: true, onTick: cleaner }, // Every 2 hours
                {
                    cronTime: '*/3 * * * *', // Every 3 minutes
                    start: true,
                    onTick: () => {
                        const { redis } = server;
                        analyzing(redis);
                    }
                }
            ]
        });

        server.get('/', (_, response) => response.send({ status: 'Running ...' }));

        server.get('/stock', async (_, response) => {
            const { redis } = server;
            await redis.set(STOCK_STATE.SYNC, 'true');
            await syncStock();
            await redis.set(STOCK_STATE.STOP, 'true');
            await redis.del(STOCK_STATE.SYNC, STOCK_STATE.CACHED);
            response.send({ status: 'Synced ...' });
        });

        server.get('/print/:section/:id', async (request, response) => {
            const { section, id } = request.params as any;
            let printed = false;

            if (id && section) {
                const printPath = `public/storage/document/${section}-${id}-${dayjs().unix()}.pdf`;

                try {
                    const browser = await puppeteer.launch({ headless: true, args: ['--disable-dev-shm-usage', '--no-sandbox', '--disable-setuid-sandbox'] });
                    const page = await browser.newPage();
                    await page.goto(`${process.env.NEXT_PUBLIC_BASE_URL ?? ''}/printout/${section}/${id}`, { waitUntil: 'networkidle0' });
                    const generated = await page.pdf({ path: printPath, format: 'A4', landscape: true });
                    await browser.close();

                    if (generated) {
                        const device = await pickRandPrinter();

                        if (device) {
                            const job = await print(printPath, device.printer, ['-o landscape', '-o fit-to-page', '-o media=A4']);
                            printed = await awaitPrinting(job);
                        }
                    }
                } catch (error) {
                    console.error(error);
                }
            }

            response.send({ printed });
        });

        server.get('/take/stock', async (_, response) => {
            const { redis } = server;
            const archived = await stockTaking(redis);
            await redis.del(STOCK_STATE.TAKE);
            response.send({ status: !archived ? 'Failed !!!' : 'Archived ...' });
        });

        server.get('/history', async (_, response) => response.send(await getArchivedRecords('period')));

        server.get('/history/:model/:period', async (request, response) => {
            const { model, period } = request.params as any;
            const records = await getArchivedRecords(model, period);
            response.send(records);
        });

        await server.listen({ port: 5000 });
        console.info('Fastify server is running ...');
    } catch (_) {
        console.error(_);
    }
};

goRun();
